package com.pmw790.power.diagram;

import com.nomagic.magicdraw.core.Project;
import com.nomagic.magicdraw.openapi.uml.PresentationElementsManager;
import com.nomagic.magicdraw.openapi.uml.ReadOnlyElementException;
import com.nomagic.magicdraw.uml.BaseElement;
import com.nomagic.magicdraw.uml.symbols.DiagramPresentationElement;
import com.nomagic.magicdraw.uml.symbols.PresentationElement;
import com.nomagic.uml2.ext.jmi.helpers.StereotypesHelper;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.*;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.AggregationKindEnum;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.VisibilityKindEnum;
import com.nomagic.uml2.ext.magicdraw.compositestructures.mdports.Port;
import com.nomagic.uml2.ext.magicdraw.mdprofiles.Stereotype;
import com.pmw790.power.functions.ConnectionRegistry;
import com.pmw790.power.functions.SysMLStereotypes;
import com.pmw790.power.functions.Utilities;

import java.util.*;

import static com.pmw790.power.functions.Utilities.Log;

/**
 * Manages diagram elements for power diagrams
 */
public class PowerDiagramElementManager {

    //--------------------------------------------------------------------------
    // SECTION 1: DIAGRAM ELEMENT MANAGEMENT
    //--------------------------------------------------------------------------

    /**
     * Adds power assets to the parametric diagram
     */
    public static synchronized void addPowerAssetsToDiagram(DiagramPresentationElement diagram, CabinetDiagramContext context)
            throws ReadOnlyElementException {
        Project project = context.getProject();
        ConnectionRegistry registry = context.getConnectionRegistry();
        Map<String, Map<String, Object>> elementPropsMap = registry.getElementProperties();
        Class cabinetBlock = context.getCabinetBlock();

        Set<String> addedConsumerNames = new HashSet<>();
        Map<Property, Element> allPropertiesToAdd = new LinkedHashMap<>(); // Will contain both part properties and external_load properties
        Map<String, Map<String, Object>> propertiesToRedefine = new HashMap<>();

        // Log additional context information if in room context
        if (context.isRoomContext()) {
            Log("Processing cabinet " + context.getCabinetName() + " in room " + context.getRoomName());
        }

        // First pass: collect all providers (including those without consumers), their consumers, and create external_load properties
        Map<String, Property> providerExternalLoadProperties = new HashMap<>();
        for (String providerName : context.getProviders()) {
            List<String> consumerNames = context.getConsumersForProvider(providerName);

            // 1. Add provider part property
            Property providerPartProperty = context.getPartProperty(providerName);
            if (providerPartProperty != null && providerPartProperty.getType() != null) {
                Element providerType = providerPartProperty.getType();

                // Add provider to the collection
                allPropertiesToAdd.put(providerPartProperty, providerType);

                // Queue property redefinition
                Map<String, Object> providerRegistryProps = elementPropsMap.get(providerName);
                if (providerRegistryProps != null) {
                    propertiesToRedefine.put(providerType.getID(), providerRegistryProps);
                }

                // 2. Process all consumers for this provider
                for (String consumerName : consumerNames) {
                    // Skip if already processed
                    if (addedConsumerNames.contains(consumerName)) continue;

                    Property consumerPartProperty = context.getPartProperty(consumerName);
                    if (consumerPartProperty != null && consumerPartProperty.getType() != null) {
                        Element consumerType = consumerPartProperty.getType();

                        // Add consumer to collection
                        allPropertiesToAdd.put(consumerPartProperty, consumerType);
                        addedConsumerNames.add(consumerName);

                        // Queue property redefinition
                        Map<String, Object> consumerRegistryProps = elementPropsMap.get(consumerName);
                        if (consumerRegistryProps != null) {
                            propertiesToRedefine.put(consumerType.getID(), consumerRegistryProps);
                        }
                    }
                }

                // 3. Get or create provider-specific external_load property
                String propName = "external_load_" + providerName;

                // Try to get from cache (already preloaded during context initialization)
                Property externalLoadProperty = context.getExternalLoadProperty(providerName);

                // If not found in cache, create it
                if (externalLoadProperty == null) {
                    externalLoadProperty = createPowerProperty(project, cabinetBlock, propName);

                    if (externalLoadProperty != null) {
                        // Set default value
                        try {
                            LiteralReal defaultValue = project.getElementsFactory().createLiteralRealInstance();
                            defaultValue.setValue(0.0);
                            defaultValue.setOwner(externalLoadProperty);
                            externalLoadProperty.setDefaultValue(defaultValue);
                        } catch (Exception e) {
                            Log("Error setting default value for " + propName + ": " + e.getMessage());
                        }

                        // Cache the newly created property
                        context.cacheExternalLoadProperty(providerName, externalLoadProperty);
                    }
                }

                // Store the property in the map and add to the collection for diagram addition
                if (externalLoadProperty != null) {
                    providerExternalLoadProperties.put(providerName, externalLoadProperty);
                    allPropertiesToAdd.put(externalLoadProperty, externalLoadProperty.getType());
                }
            }
        }

        // Add all collected properties to the diagram in a single batch
        if (!allPropertiesToAdd.isEmpty()) {
            synchronized (diagram) {
                addElementsToDiagram(context, diagram, allPropertiesToAdd.keySet().toArray(new Element[0]));
            }
        } else {
            Log("No properties to add to diagram for cabinet " + context.getCabinetName());
            return;
        }

        // Second pass: redefine properties in batch
        for (Map.Entry<String, Map<String, Object>> entry : propertiesToRedefine.entrySet()) {
            String elementId = entry.getKey();
            Map<String, Object> props = entry.getValue();
            try {
                BaseElement baseElement = project.getElementByID(elementId);
                if (baseElement instanceof Element) {
                    Element element = (Element) baseElement;
                    redefineInheritedPowerProperties(project, element, props);
                }
            } catch (Exception e) {
                Log("Error retrieving element by ID: " + e.getMessage());
            }
        }

        // Final pass: add value properties to parts
        addValuePropertiesToParts(diagram, registry, elementPropsMap, context);
    }

    /**
     * Adds value properties to parts in the diagram
     */
    private static void addValuePropertiesToParts(DiagramPresentationElement diagram, ConnectionRegistry registry,
                                                Map<String, Map<String, Object>> elementPropsMap, CabinetDiagramContext context) {
        PresentationElementsManager manager = PresentationElementsManager.getInstance();
        Map<PresentationElement, List<Property>> valuePropertiesToAdd = new HashMap<>();

        // Collect all value properties to add
        for (PresentationElement presElement : diagram.getPresentationElements()) {
            Element modelElement = presElement.getElement();

            // Check if it's a Property representing a Part with proper aggregation
            if (modelElement instanceof Property &&
                ((Property) modelElement).getAggregation() != AggregationKindEnum.NONE) {

                Property partProperty = (Property) modelElement;
                Element partType = partProperty.getType();

                if (partType instanceof Class) {
                    Class partClass = (Class) partType;
                    String partTypeName = partClass.getName();

                    // Check if this part's type is a known power provider or consumer
                    String elementType = registry.getElementType(partTypeName);
                    if (elementType.equals(Utilities.TYPE_POWER_PROVIDER) || elementType.equals(Utilities.TYPE_POWER_CONSUMER)) {
                        // Get properties from registry for this element
                        Map<String, Object> propsForElement = elementPropsMap.get(partTypeName);
                        if (propsForElement == null || propsForElement.isEmpty()) {
                            continue;
                        }

                        // Get property names from registry props
                        Set<String> propNames = propsForElement.keySet();

                        // Collect value properties to display
                        List<Property> valueProps = new ArrayList<>();

                        // Also create a map for caching
                        Map<String, Property> valuePropsMap = new HashMap<>();

                        for (Property valueProperty : partClass.getOwnedAttribute()) {
                            // Only add properties that are in the props map and not ports
                            String propName = valueProperty.getName();
                            if (!(valueProperty instanceof Port) && propName != null && !propName.equals("host_asset") && !propName.equals("host_location") && propNames.contains(propName)) {
                                valueProps.add(valueProperty);
                                // Also add to the map for caching
                                valuePropsMap.put(propName, valueProperty);
                            }
                        }

                        if (!valueProps.isEmpty()) {
                            valuePropertiesToAdd.put(presElement, valueProps);

                            // Cache the value properties if context is available
                            if (context != null) {
                                context.cacheValueProperties(partTypeName, valuePropsMap);
                            }
                        }
                    }
                }
            }
        }

        // Add all value properties in batch for each container
        for (Map.Entry<PresentationElement, List<Property>> entry : valuePropertiesToAdd.entrySet()) {
            PresentationElement container = entry.getKey();
            List<Property> valueProps = entry.getValue();

            // Create all value properties for this container in a single batch operation
            try {
                for (Property valueProperty : valueProps) {
                    try {
                        manager.createShapeElement(valueProperty, container, true);
                    } catch (Exception e) {
                        if (e instanceof ReadOnlyElementException) {
                            // Less severe - just log briefly
                            Log("Read-only error for property: " + valueProperty.getName());
                        } else {
                            // More severe - log details
                            Log("Error adding value property '" + valueProperty.getName() +
                                    "' to part '" + ((NamedElement) Objects.requireNonNull(container.getElement())).getName() + "': " + e.getMessage());
                        }
                    }
                }
            } catch (Exception e) {
                Log("Error processing value properties for part '" + ((NamedElement) Objects.requireNonNull(container.getElement())).getName() + "': " + e.getMessage());
            }
        }
    }

    /**
     * Creates constraint properties typed by constraint blocks for each power provider
     */
    public static void addConstraintPropertiesToDiagram(DiagramPresentationElement diagram, CabinetDiagramContext context) {
        try {
            // Log additional context information if in room context
            if (context.isRoomContext()) {
                Log("Adding constraint properties for cabinet " + context.getCabinetName() + " in room " + context.getRoomName());
            }

            // Get the Power Calculation package's constraint blocks
            Map<String, Class> constraintBlocks = SysMLStereotypes.getPowerCalculationConstraintBlocks();
            if (constraintBlocks.isEmpty()) {
                Log("Error: No constraint blocks found in Power Calculation package. Cannot add constraint properties.");
                return;
            }

            // Get the cabinet block that will own the constraint properties
            Class cabinetBlock = context.getCabinetBlock();
            if (cabinetBlock == null) {
                Log("Error: Cabinet block is null. Cannot add constraint properties.");
                return;
            }

            // Get list of all providers
            List<String> allProviders = context.getProviders();
            if (allProviders.isEmpty()) {
                return;
            }

            Project project = context.getProject();

            // Create a list to collect all constraint properties
            List<Property> constraintProperties = new ArrayList<>();

            // Process all providers
            for (String providerName : allProviders) {
                for (Map.Entry<String, Class> entry : constraintBlocks.entrySet()) {
                    addConstraintPropertyForProvider(
                        project, cabinetBlock, providerName, entry.getKey(), entry.getValue(),
                        constraintProperties, context);
                }
            }

            // Add all constraint properties to the diagram using addElementsToDiagram
            if (!constraintProperties.isEmpty()) {
                // Convert list to array for addElementsToDiagram
                Element[] elementsToAdd = constraintProperties.toArray(new Element[0]);
                addElementsToDiagram(context, diagram, elementsToAdd);
            }

        } catch (Exception e) {
            Log("Error adding constraint properties to diagram: " + e.getMessage());
        }
    }

    /**
     * Helper method to add a constraint property for a provider
     * Avoids duplicating code for top and child providers
     */
    private static void addConstraintPropertyForProvider(
            Project project,
            Class cabinetBlock,
            String providerName,
            String constraintBlockName,
            Class constraintBlock,
            List<Property> constraintProperties,
            CabinetDiagramContext context) {

        // Create a unique name for this constraint property based on the provider
        String constraintPropName = providerName + "_" + constraintBlockName;

        // Check if a constraint property with this name already exists in the cabinet block
        Property constraintProp = Utilities.ModelElements.findPropertyByName(cabinetBlock, constraintPropName);

        if (constraintProp == null) {
            // No existing property, create a new one owned by the cabinet block
            try {
                constraintProp = project.getElementsFactory().createPropertyInstance();
                constraintProp.setName(constraintPropName);
                constraintProp.setType(constraintBlock);
                constraintProp.setAggregation(AggregationKindEnum.COMPOSITE);
                constraintProp.setOwner(cabinetBlock);
            } catch (Exception e) {
                Log("Error creating constraint property " + constraintPropName + ": " + e.getMessage());
                return;
            }
        }

        // Add the constraint property to our collection
        constraintProperties.add(constraintProp);

        // Cache the constraint property in the context for reuse by PowerConnectorManager
        context.cacheConstraintProperty(providerName, constraintBlockName, constraintProp);

        // Cache the constraint ports for later use in addElementsToDiagram and by PowerConnectorManager
        List<Property> ports = new ArrayList<>(constraintBlock.getOwnedAttribute());
        if (!ports.isEmpty()) {
            // Create a map of port name to port property
            Map<String, Property> portsMap = new HashMap<>();
            for (Property port : ports) {
                if (port.getName() != null) {
                    portsMap.put(port.getName(), port);
                }
            }

            // Cache the ports
            if (!portsMap.isEmpty()) {
                context.cacheConstraintPorts(providerName, constraintBlockName, portsMap);
            }
        }
    }

    //--------------------------------------------------------------------------
    // SECTION 2: PROPERTY INHERITANCE AND REDEFINITION
    //--------------------------------------------------------------------------

    /**
     * Redefines inherited power properties in the given element
     */
    public static void redefineInheritedPowerProperties(Project project, Element element, Map<String, Object> props) {
        if (!(element instanceof Class)) return;

        Class classElement = (Class) element;
        String elementName = ((NamedElement)element).getName();

        // Dynamically collect properties to redefine from the props map
        Set<String> propsToRedefine = new HashSet<>(props.keySet());

        // Cache the ValueProperty stereotype - only need to get this once
        Stereotype valuePropertyStereotype = SysMLStereotypes.getValuePropertyStereotype();

        // Find existing owned properties in one pass
        Map<String, Property> existingProps = new HashMap<>();
        for (Element owned : element.getOwnedElement()) {
            if (owned instanceof Property && !(owned instanceof Port)) {
                String name = ((Property)owned).getName();
                if (propsToRedefine.contains(name)) {
                    existingProps.put(name, (Property)owned);
                }
            }
        }

        // Check if we have cached property objects from previous collectProperties call
        Map<String, Property> cachedProps = Utilities.ModelElements.getCachedPropertyObjects(element);

        // Collect properties that need to be looked up in hierarchy
        Set<String> propsToLookup = new HashSet<>();
        Map<String, Property> inheritedProps = new HashMap<>();

        // First check if we have cached property objects
        if (cachedProps != null && !cachedProps.isEmpty()) {
            // Use cached property objects first, but validate them to ensure they're still valid
            for (String propName : propsToRedefine) {
                if (!existingProps.containsKey(propName) && cachedProps.containsKey(propName)) {
                    Property cachedProp = cachedProps.get(propName);
                    // Validate the cached property
                    try {
                        if (cachedProp != null) {
                            String propId = cachedProp.getID();
                            if (propId != null && !propId.isEmpty()) {
                                // Property is valid, use it
                                inheritedProps.put(propName, cachedProp);
                                continue;
                            }
                        }
                    } catch (Exception e) {
                        // Property is invalid, will look it up instead
                    }
                    // If we get here, the cached property was invalid
                    propsToLookup.add(propName);
                } else if (!existingProps.containsKey(propName)) {
                    propsToLookup.add(propName);
                }
            }
        } else {
            // No cached properties, need to look up all missing properties
            for (String propName : propsToRedefine) {
                if (!existingProps.containsKey(propName)) {
                    propsToLookup.add(propName);
                }
            }
        }

        // Only perform hierarchy lookup for properties not found in cache
        if (!propsToLookup.isEmpty()) {
            Map<String, Property> lookupProps = Utilities.ModelElements.findPropertiesInHierarchy(classElement, propsToLookup);
            inheritedProps.putAll(lookupProps);
        }

        // Handle properties that need redefinition
        for (String propName : propsToRedefine) {
            Object value = props.get(propName);

            try {
                Property existingProp = existingProps.get(propName);

                if (existingProp != null) {
                    // Update existing property
                    setPropertyValue(project, existingProp, value);
                } else {
                    // Get from the pre-fetched inherited properties
                    Property inheritedProp = inheritedProps.get(propName);

                    if (inheritedProp != null) {
                        Property newProp = project.getElementsFactory().createPropertyInstance();
                        newProp.setName(propName);
                        newProp.setVisibility(VisibilityKindEnum.PUBLIC);
                        newProp.setType(inheritedProp.getType());
                        newProp.setOwner(element);
                        newProp.getRedefinedProperty().add(inheritedProp);

                        // Set value directly
                        setPropertyValue(project, newProp, value);

                        // Only copy ValueProperty stereotype if present (using cached reference)
                        if (StereotypesHelper.hasStereotype(inheritedProp, valuePropertyStereotype)) {
                            StereotypesHelper.addStereotype(newProp, valuePropertyStereotype);
                        }
                    } else {
                        Log("ERROR: Expected inherited property '" + propName + "' not found for " + elementName);
                        Log("Skipping property creation for '" + propName + "' since it should be inherited");
                    }
                }
            } catch (Exception e) {
                Log("Error setting property " + propName + ": " + e.getMessage());
            }
        }
    }

    //--------------------------------------------------------------------------
    // SECTION 3: PROPERTY OPERATIONS
    //--------------------------------------------------------------------------

    /**
     * Creates a power-related property on a class element with the specified name
     */
    private static Property createPowerProperty(Project project, Class classElement, String propName) {
        try {
            // Get the power type
            Type powerType = SysMLStereotypes.findISO80000Type("power[watt]");
            if (powerType == null) {
                Log("Warning: ISO type 'power[watt]' not found. Cannot create " + propName + " property for " + classElement.getName());
                return null;
            }

            // Create the property
            Property property = project.getElementsFactory().createPropertyInstance();
            property.setName(propName);
            property.setVisibility(VisibilityKindEnum.PUBLIC);
            property.setType(powerType);
            property.setOwner(classElement);

            // Add ValueProperty stereotype
            Stereotype valuePropertyStereotype = SysMLStereotypes.getValuePropertyStereotype();
            if (valuePropertyStereotype != null) {
                StereotypesHelper.addStereotype(property, valuePropertyStereotype);
            } else {
                Log("Warning: ValueProperty stereotype not found. Cannot apply to " + propName + " for " + classElement.getName());
            }

            return property;
        } catch (Exception e) {
            Log("Error creating " + propName + " property for " + classElement.getName() + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Sets a value on a property
     */
    public static void setPropertyValue(Project project, Property property, Object value) {
        try {
            // Check if the property is valid and accessible
            if (property == null) {
                Log("Warning: Cannot set value on null property");
                return;
            }

            // Safer approach to check if property belongs to current project
            try {
                Element owner = property.getOwner();
                if (owner == null) {
                    Log("Warning: Property " + property.getName() + " has no owner. Skipping value setting.");
                    return;
                }
            } catch (Exception e) {
                Log("Warning: Property " + property.getName() + " appears to be from a different repository or is invalid. Skipping value setting.");
                return;
            }

            // Check if there's an existing default value that needs to be removed
            ValueSpecification existingValue = null;
            try {
                existingValue = property.getDefaultValue();
                if (existingValue != null) {
                    // Remove the existing value first to avoid cross-repository issues
                    property.setDefaultValue(null);
                }
            } catch (Exception e) {
                Log("Warning: Could not access or clear existing default value for " + property.getName() + ": " + e.getMessage());
                // Continue anyway - we'll try to set the new value
            }

            // Create a new value specification from the current project
            ValueSpecification valueSpec;
            if (value instanceof Double) {
                LiteralReal realValue = project.getElementsFactory().createLiteralRealInstance();
                realValue.setValue((Double) value);
                valueSpec = realValue;
            } else if (value instanceof Integer) {
                LiteralReal realValue = project.getElementsFactory().createLiteralRealInstance();
                realValue.setValue(((Integer) value).doubleValue());
                valueSpec = realValue;
            } else {
                LiteralString stringValue = project.getElementsFactory().createLiteralStringInstance();
                stringValue.setValue(value.toString());
                valueSpec = stringValue;
            }

            // Set the owner and default value
            try {
                valueSpec.setOwner(property);
                property.setDefaultValue(valueSpec);
            } catch (Exception e) {
                Log("Error setting default value for property " + property.getName() + ": " + e.getMessage());
                // If this fails, try a different approach - create the value spec with the property as owner
                try {
                    ValueSpecification altValueSpec;
                    if (value instanceof Double || value instanceof Integer) {
                        LiteralReal realValue = project.getElementsFactory().createLiteralRealInstance();
                        realValue.setValue(value instanceof Double ? (Double)value : ((Integer)value).doubleValue());
                        altValueSpec = realValue;
                    } else {
                        LiteralString stringValue = project.getElementsFactory().createLiteralStringInstance();
                        stringValue.setValue(value.toString());
                        altValueSpec = stringValue;
                    }

                    // Set owner first, then set as default value
                    property.setDefaultValue(altValueSpec);
                    altValueSpec.setOwner(property);
                } catch (Exception ex) {
                    Log("Failed alternative approach for setting property " + property.getName() + ": " + ex.getMessage());
                }
            }
        } catch (Exception e) {
            Log("Error setting property " + (property != null ? property.getName() : null) + ": " + e.getMessage());
        }
    }

    /**
     * Adds elements to a diagram
     */
    public static synchronized void addElementsToDiagram(CabinetDiagramContext context, DiagramPresentationElement diagram, Element[] elements) {
        try {
            Map<Element, Property> partProperties = new LinkedHashMap<>();
            Map<Element, Property> constraintProperties = new LinkedHashMap<>();

            // Pre-sort elements into appropriate categories in a single pass
            for (Element element : elements) {
                if (element instanceof Property) {
                    Property property = (Property) element;
                    Element type = property.getType();

                    // Cache the property type check result
                    if (type instanceof Class) {
                        boolean isConstraint = Utilities.ModelElements.isConstraintBlock(type);
                        if (isConstraint) {
                            constraintProperties.put(element, property);
                        } else {
                            partProperties.put(element, property);
                        }
                    } else {
                        partProperties.put(element, property);
                    }
                }
            }

            // Batch creation of part properties for better performance
            if (!partProperties.isEmpty()) {
                // Create all part properties at once if possible
                PresentationElementsManager manager = PresentationElementsManager.getInstance();
                for (Element element : partProperties.keySet()) {
                    manager.createShapeElement(element, diagram, true);
                }
            }

            // Then add constraint properties and their ports
            if (!constraintProperties.isEmpty()) {
                PresentationElementsManager manager = PresentationElementsManager.getInstance();
                Map<PresentationElement, List<Property>> portsToAdd = new HashMap<>();

                // First pass: create all constraint property shapes and get ports from cache
                for (Map.Entry<Element, Property> entry : constraintProperties.entrySet()) {
                    Element element = entry.getKey();
                    Property property = entry.getValue();
                    PresentationElement presentationElement = manager.createShapeElement(element, diagram, true);

                    // Process the presentation element
                    if (presentationElement != null) {
                        // If this is a constraint property, get its ports (preferably from cache)
                        if (property.getType() instanceof Class) {
                            Class constraintBlock = (Class) property.getType();
                            if (Utilities.ModelElements.isConstraintBlock(constraintBlock)) {
                                // Try to get ports from cache first
                                List<Property> ports = null;
                                String propertyName = property.getName();

                                if (propertyName != null && propertyName.contains("_")) {
                                    String constraintTypeName = constraintBlock.getName();
                                    if (constraintTypeName != null) {
                                        String providerName = null;

                                        // Find the last underscore in the property name
                                        int lastUnderscoreIndex = propertyName.lastIndexOf("_");
                                        if (lastUnderscoreIndex > 0) { // Ensure there's text before the underscore
                                            // Extract everything before the last underscore (including any previous underscores)
                                            providerName = propertyName.substring(0, lastUnderscoreIndex);
                                            // Get ports from cache
                                            Map<String, Property> cachedPorts = context.getConstraintPorts(providerName, constraintTypeName);
                                            if (cachedPorts != null && !cachedPorts.isEmpty()) {
                                                ports = new ArrayList<>(cachedPorts.values());
                                            }
                                        }
                                    }
                                }

                                // If not in cache, collect directly
                                if (ports == null) {
                                    ports = new ArrayList<>(constraintBlock.getOwnedAttribute());
                                }

                                if (!ports.isEmpty()) {
                                    portsToAdd.put(presentationElement, ports);
                                }
                            }
                        }
                    }
                }

                // Second pass: add all ports to their respective constraint properties
                for (Map.Entry<PresentationElement, List<Property>> entry : portsToAdd.entrySet()) {
                    PresentationElement container = entry.getKey();
                    List<Property> ports = entry.getValue();

                    for (Property port : ports) {
                        try {
                            PresentationElement portPE = manager.createShapeElement(port, container, true);
                        } catch (Exception e) {
                            Log("Error adding port " + port.getName() + ": " + e.getMessage());
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log("Error adding elements to diagram: " + e.getMessage());
        }
    }
}